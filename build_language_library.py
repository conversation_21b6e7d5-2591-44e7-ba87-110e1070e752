# Helper script to build the Tree-sitter language library for your knowledge graph generator
# Usage: python build_language_library.py

import os
from tree_sitter import Language

# Paths to grammar repositories (update if you clone elsewhere)
GRAMMARS = [
    os.path.abspath('./tree-sitter-python'),
    os.path.abspath('./tree-sitter-javascript'),
    # Add more grammar paths as needed
]

LIB_PATH = os.path.abspath('build/my-languages.so')

os.makedirs(os.path.dirname(LIB_PATH), exist_ok=True)

print('Building language library...')
Language.build_library(
    LIB_PATH,
    GRAMMARS
)
print(f'Language library built at {LIB_PATH}')
