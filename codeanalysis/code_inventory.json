{"main.py": {"type": "SourceFile", "entities": [{"name": "parse_args", "type": "Function"}, {"name": "chunk_file", "type": "Function"}, {"name": "get_nlp", "type": "Function"}, {"name": "get_hf_ner_pipeline", "type": "Function"}, {"name": "process_chunk_spacy", "type": "Function"}, {"name": "process_chunk_hf", "type": "Function"}, {"name": "process_chunk", "type": "Function"}, {"name": "worker_thread", "type": "Function"}, {"name": "test_spacy_ner", "type": "Function"}, {"name": "save_output", "type": "Function"}]}, "generate_knowledge_graph.py": {"type": "SourceFile", "entities": [{"name": "setup_treesitter_grammars", "type": "Function"}, {"name": "get_gitignore", "type": "Function"}, {"name": "categorize_file", "type": "Function"}, {"name": "add_file_node", "type": "Function"}, {"name": "parse_requirements", "type": "Function"}, {"name": "parse_python_code", "type": "Function"}, {"name": "main", "type": "Function"}]}, "build_language_library.py": {"type": "SourceFile", "entities": []}, "lib/bindings/utils.js": {"type": "SourceFile", "entities": [{"name": "neighbourhoodHighlight", "type": "Function"}, {"name": "filterHighlight", "type": "Function"}, {"name": "selectNode", "type": "Function"}, {"name": "selectNodes", "type": "Function"}, {"name": "highlightFilter", "type": "Function"}]}, "lib/tom-select/tom-select.css": {"type": "SourceFile", "entities": [{"name": ".ts-wrapper", "type": "CSSClassSelector"}, {"name": ".plugin-drag_drop", "type": "CSSClassSelector"}, {"name": ".plugin-checkbox_options", "type": "CSSClassSelector"}, {"name": ".plugin-clear_button", "type": "CSSClassSelector"}, {"name": ".clear-button", "type": "CSSClassSelector"}]}, "lib/tom-select/tom-select.complete.min.js": {"type": "SourceFile", "entities": []}, "lib/vis-9.1.2/vis-network.css": {"type": "SourceFile", "entities": []}, "lib/vis-9.1.2/vis-network.min.js": {"type": "SourceFile", "entities": []}, "requirements.txt": {"type": "ConfigFile", "entities": []}, "output.json": {"type": "ConfigFile", "entities": []}, ".gitignore": {"type": "ConfigFile", "entities": []}, ".plandex-v2/projects-v2.json": {"type": "ConfigFile", "entities": []}, "ThirdPartyNoticeText.txt": {"type": "DocumentationFile", "entities": []}, "sample.txt": {"type": "DocumentationFile", "entities": []}, "spec-prompt.md": {"type": "DocumentationFile", "entities": []}}