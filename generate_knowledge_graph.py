import os
import json
import re
import logging
from pathlib import Path

from tree_sitter import Parser, Language, Query
import tree_sitter_python
from tree_sitter_languages import get_language

import pathspec

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Tree-sitter Language Setup ---


def setup_treesitter_grammars():
    logging.info("Using pre-built Tree-sitter Python grammar.")




def get_gitignore(root_dir: Path) -> pathspec.PathSpec:
    """Reads .gitignore from the root directory and returns a pathspec object."""
    ignore_file = root_dir / '.gitignore'
    patterns = []
    if ignore_file.is_file():
        with open(ignore_file, 'r') as f:
            patterns = f.readlines()
    # Add common patterns to always ignore
    patterns.extend(['.git/', '.venv/', '__pycache__/', '*.pyc'])
    return pathspec.PathSpec.from_lines('gitwildmatch', patterns)

def categorize_file(file_path: Path) -> str:
    """Categorizes a file based on its extension and name."""
    if 'test' in file_path.name or '.spec.' in file_path.name:
        return "TestFile"
    if file_path.name in ['requirements.txt', 'package.json', 'pom.xml', 'Cargo.toml', 'go.mod']:
        return "BuildFile"
    if file_path.suffix in ['.md', '.rst']:
        return "DocumentationFile"
    if file_path.suffix in ['.py', '.java', '.js', '.go', '.rs', '.c', '.cpp', '.h']:
        return "SourceFile"
    if file_path.suffix in ['.json', '.yml', '.yaml', '.toml', '.ini', '.cfg']:
        return "ConfigFile"
    return "OtherFile"

def add_file_node(file_path: Path, root_dir: Path) -> tuple[str, str, str]:
    """Determines the node ID, label, and Mermaid shape for a file."""
    relative_path = file_path.relative_to(root_dir).as_posix()
    node_id = f"file_{relative_path.replace('.', '_').replace('/', '_').replace('-', '_')}"
    file_name = file_path.name
    category = categorize_file(file_path)

    shape_map = {
        "SourceFile": f'{node_id}["{file_name}"]',
        "DocumentationFile": f'{node_id}[("{file_name}")]',
        "ConfigFile": f'{node_id}[("{file_name}")]',
        "TestFile": f'{node_id}[/"{file_name}"/]',
        "BuildFile": f'{node_id}{{"{file_name}"}}',
        "OtherFile": f'{node_id}["{file_name}"]', # Default to rectangle
    }
    mermaid_node_syntax = shape_map.get(category, f'{node_id}["{file_name}"]')
    return node_id, category, mermaid_node_syntax

def parse_requirements(file_path: Path, file_node_id: str, existing_nodes: set, mermaid_nodes: list, mermaid_links: list):
    """Parses a requirements.txt file and adds dependency nodes."""
    logging.info(f"Parsing dependencies from {file_path}")
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                # Use regex to robustly extract the package name, ignoring versions, extras, etc.
                match = re.match(r'^\s*([a-zA-Z0-9_.-]+)', line)
                if match:
                    dep_name = match.group(1)
                    dep_id = f"dep:{dep_name}"
                    if dep_id not in existing_nodes:
                        mermaid_nodes.append(f'    {dep_id}{{"{dep_name}"}}')
                        existing_nodes.add(dep_id)
                    mermaid_links.append(f'    {file_node_id} -- "imports" --> {dep_id}')

def parse_python_code(file_path: Path, file_node_id: str, existing_nodes: set, mermaid_nodes: list, mermaid_links: list):
    """Parses a Python file to find code constructs and relationships."""
    try:
        py_language = Language(tree_sitter_python.language())
        parser = Parser(py_language)
    except Exception as e:
        logging.error(f"Could not load Python grammar or set parser language, cannot parse file. Error: {e}")
        return

    logging.info(f"Parsing source code from {file_path}")
    with open(file_path, 'r', errors='ignore') as f:
        content = f.read()

    tree = parser.parse(bytes(content, "utf8"))

    # --- Queries for Python constructs ---
    # These can be extended for other languages by loading the appropriate grammar
    # and writing similar queries.
    queries = {
        "Function": "(function_definition name: (identifier) @name)",
        "Class": "(class_definition name: (identifier) @name)",
        "Import": "(import_from_statement module_name: (dotted_name) @name)",
        "Call": "(call function: (identifier) @name)"
    }

    constructs_in_file = {}

    # 1. Find all top-level constructs (functions, classes)
    for construct_type, query_str in [("Function", queries["Function"]), ("Class", queries["Class"])]:
        query = py_language.query(query_str)
        matches = query.matches(tree.root_node)

        for match in matches:
             for node, _ in match:
                 construct_name = node.text.decode('utf8')
                 construct_id = f"cc:{construct_name}@{file_path.as_posix()}"
                 constructs_in_file[construct_name] = construct_id

                 if construct_id not in existing_nodes:
                     if construct_type == "Class":
                          mermaid_nodes.append(f'        {construct_id}[["{construct_name}"]]')
                       elif construct_type == "Function":
                         mermaid_nodes.append(f'        {construct_id}(("{construct_name}"))')
                         existing_nodes.add(construct_id)
                     mermaid_links.append(f'    {file_node_id} --> {construct_id}')

    # 2. Find relationships (calls, imports)
    # This is a simplified approach. A full implementation would require
    # scope resolution to accurately map calls to their definitions.

    # Find function calls
    call_query = PY_LANGUAGE.query(queries["Call"])
    call_captures = call_query.captures(tree.root_node)

    # Find imports and link to dependencies
    import_query = py_language.query(queries["Import"])
    import_captures = import_query.captures(tree.root_node)
    for node, _ in import_captures:
        module_name = node.text.decode('utf8').split('.')[0]
        dep_id = f"dep:{module_name}"
        if dep_id not in existing_nodes:
            mermaid_nodes.append(f'    {dep_id}{{" {module_name}"}}')
            existing_nodes.add(dep_id)
        mermaid_links.append(f'    {file_node_id} -- "IMPORTS" --> {dep_id}')

    # Find the containing function/class for each call
    for call_node, _ in call_captures:
        called_name = call_node.text.decode('utf8')

        # Find the function/class this call is inside
        parent = call_node.parent
        caller_id = None
        while parent:
            if parent.type in ['function_definition', 'class_definition']:
                name_node = parent.child_by_field_name('name')
                if name_node:
                    caller_name = name_node.text.decode('utf8')
                    caller_id = f"cc:{caller_name}@{file_path.as_posix()}"
                    break
            parent = parent.parent

        if not caller_id:
            caller_id = file_node_id # Assume file-level call if not in a function/class

        # If the called function is in the same file
        if called_name in constructs_in_file:
            callee_id = constructs_in_file[called_name]
            mermaid_links.append(f'    {caller_id} --> {callee_id}')
        else:
            # This could be a reference to a construct in another file or a library
            # A more advanced implementation would search other files or dependencies.
            # For now, we can create a placeholder or ignore.
            pass

    # Find imports and link to dependencies



def main():
    setup_treesitter_grammars()

    root_dir = Path(__file__).parent
    gitignore_spec = get_gitignore(root_dir)

    mermaid_nodes = []
    mermaid_links = []
    existing_nodes = set()

    # Collect all files first
    all_files = []
    for file_path in root_dir.rglob("*"):
        if file_path.is_file() and not gitignore_spec.match_file(file_path.as_posix()):
            all_files.append(file_path)

    # Process files and generate Mermaid syntax
    for file_path in all_files:
        node_id, category, mermaid_node_syntax = add_file_node(file_path, root_dir)

        # Add file node to a subgraph for its directory or directly if at root
        relative_path_str = file_path.relative_to(root_dir).as_posix()
        parent_dir = Path(relative_path_str).parent.as_posix()

        if parent_dir == '.': # Root directory files
            mermaid_nodes.append(f'    {mermaid_node_syntax}')
        else:
            # Create a subgraph for the directory
            # Sanitize directory name for Mermaid subgraph ID
            subgraph_id = f'subgraph_{parent_dir.replace("/", "_").replace(".", "_")}'
            mermaid_nodes.append(f'  subgraph "{parent_dir}"')
            mermaid_nodes.append(f'    {mermaid_node_syntax}')
            mermaid_nodes.append(f'  end')

        existing_nodes.add(node_id)

        if category == "BuildFile" and file_path.name == "requirements.txt":
            parse_requirements(file_path, node_id, existing_nodes, mermaid_nodes, mermaid_links)
        elif category == "SourceFile" and file_path.suffix == ".py":
            parse_python_code(file_path, node_id, existing_nodes, mermaid_nodes, mermaid_links)

    # Combine all Mermaid syntax
    mermaid_syntax = "graph TD;\n\n" + "\n".join(mermaid_nodes) + "\n\n" + "\n".join(mermaid_links)

    # Generate the final HTML output
    html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Knowledge Graph</title>
</head>
<body>
    <h1>Codebase Knowledge Graph</h1>
    <pre class="mermaid">
      %% --- PASTE YOUR GENERATED MERMAID SYNTAX HERE --- %%
      {mermaid_syntax}
      %% --- END OF GENERATED SYNTAX --- %%
    </pre>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({{ startOnLoad: true }});
    </script>
</body>
</html>
"""

    output_path = root_dir / "knowledge_graph.html"
    with open(output_path, "w") as f:
        f.write(html_template)
    logging.info(f"Knowledge graph saved to {output_path}")

if __name__ == "__main__":
    main()
