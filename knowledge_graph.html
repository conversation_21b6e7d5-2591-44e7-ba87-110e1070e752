<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Knowledge Graph</title>
</head>
<body>
    <h1>Codebase Knowledge Graph</h1>
    <pre class="mermaid">
graph TD;

%% Root Level Files
main_py["main.py"]
build_language_library_py["build_language_library.py"]
generate_knowledge_graph_py["generate_knowledge_graph.py"]
requirements_txt{"requirements.txt"}
spec_prompt_md("spec-prompt.md")
ThirdPartyNoticeText_txt("ThirdPartyNoticeText.txt")
gitignore("gitignore")
output_json("output.json")
sample_txt["sample.txt"]

%% Library Files
subgraph "lib"
    subgraph "lib/bindings"
        utils_js["utils.js"]
    end
    subgraph "lib/tom-select"
        tom_select_js["tom-select.complete.min.js"]
        tom_select_css["tom-select.css"]
    end
    subgraph "lib/vis-9.1.2"
        vis_network_js["vis-network.min.js"]
        vis_network_css["vis-network.css"]
    end
end

%% Config Files
subgraph ".plandex-v2"
    projects_v2_json("projects-v2.json")
end

%% Main.py Functions
subgraph "main.py entities"
    main_parse_args(("parse_args"))
    main_chunk_file(("chunk_file"))
    main_get_nlp(("get_nlp"))
    main_get_hf_ner_pipeline(("get_hf_ner_pipeline"))
    main_process_chunk_spacy(("process_chunk_spacy"))
    main_process_chunk_hf(("process_chunk_hf"))
    main_process_chunk(("process_chunk"))
    main_worker_thread(("worker_thread"))
    main_test_spacy_ner(("test_spacy_ner"))
    main_save_output(("save_output"))
    main_thread_local[["thread_local"]]
end

%% Build Language Library Functions
subgraph "build_language_library.py entities"
    build_GRAMMARS[["GRAMMARS"]]
    build_LIB_PATH[["LIB_PATH"]]
end

%% Generate Knowledge Graph Functions
subgraph "generate_knowledge_graph.py entities"
    gen_setup_treesitter_grammars(("setup_treesitter_grammars"))
    gen_get_gitignore(("get_gitignore"))
    gen_categorize_file(("categorize_file"))
    gen_add_file_node(("add_file_node"))
    gen_parse_requirements(("parse_requirements"))
    gen_parse_python_code(("parse_python_code"))
    gen_main(("main"))
end

%% Utils.js Functions
subgraph "utils.js entities"
    utils_neighbourhoodHighlight(("neighbourhoodHighlight"))
    utils_filterHighlight(("filterHighlight"))
    utils_selectNode(("selectNode"))
    utils_selectNodes(("selectNodes"))
    utils_highlightFilter(("highlightFilter"))
end

%% Dependencies
dep_spacy{"spacy[apple]"}
dep_torch{"torch"}
dep_transformers{"transformers"}
dep_tree_sitter{"tree-sitter"}
dep_tree_sitter_python{"tree-sitter-python"}
dep_tree_sitter_languages{"tree-sitter-languages"}
dep_argparse{"argparse"}
dep_threading{"threading"}
dep_queue{"queue"}
dep_json{"json"}
dep_os{"os"}
dep_datetime{"datetime"}
dep_pathlib{"pathlib"}
dep_logging{"logging"}
dep_re{"re"}
dep_pathspec{"pathspec"}
dep_collections{"collections"}

%% File to Entity Relationships
main_py --> main_parse_args
main_py --> main_chunk_file
main_py --> main_get_nlp
main_py --> main_get_hf_ner_pipeline
main_py --> main_process_chunk_spacy
main_py --> main_process_chunk_hf
main_py --> main_process_chunk
main_py --> main_worker_thread
main_py --> main_test_spacy_ner
main_py --> main_save_output
main_py --> main_thread_local

build_language_library_py --> build_GRAMMARS
build_language_library_py --> build_LIB_PATH

generate_knowledge_graph_py --> gen_setup_treesitter_grammars
generate_knowledge_graph_py --> gen_get_gitignore
generate_knowledge_graph_py --> gen_categorize_file
generate_knowledge_graph_py --> gen_add_file_node
generate_knowledge_graph_py --> gen_parse_requirements
generate_knowledge_graph_py --> gen_parse_python_code
generate_knowledge_graph_py --> gen_main

utils_js --> utils_neighbourhoodHighlight
utils_js --> utils_filterHighlight
utils_js --> utils_selectNode
utils_js --> utils_selectNodes
utils_js --> utils_highlightFilter

%% Dependency Relationships
requirements_txt -- "defines" --> dep_spacy
requirements_txt -- "defines" --> dep_torch
requirements_txt -- "defines" --> dep_transformers
requirements_txt -- "defines" --> dep_tree_sitter
requirements_txt -- "defines" --> dep_tree_sitter_python
requirements_txt -- "defines" --> dep_tree_sitter_languages

main_py -- "imports" --> dep_argparse
main_py -- "imports" --> dep_threading
main_py -- "imports" --> dep_queue
main_py -- "imports" --> dep_spacy
main_py -- "imports" --> dep_json
main_py -- "imports" --> dep_os
main_py -- "imports" --> dep_datetime
main_py -- "imports" --> dep_torch
main_py -- "imports" --> dep_transformers
main_py -- "imports" --> dep_collections
main_py -- "imports" --> dep_re

generate_knowledge_graph_py -- "imports" --> dep_os
generate_knowledge_graph_py -- "imports" --> dep_json
generate_knowledge_graph_py -- "imports" --> dep_re
generate_knowledge_graph_py -- "imports" --> dep_logging
generate_knowledge_graph_py -- "imports" --> dep_pathlib
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter_python
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter_languages
generate_knowledge_graph_py -- "imports" --> dep_pathspec

build_language_library_py -- "imports" --> dep_os
build_language_library_py -- "imports" --> dep_tree_sitter

%% Function Call Relationships
main_process_chunk -- "calls" --> main_process_chunk_spacy
main_process_chunk -- "calls" --> main_process_chunk_hf
main_worker_thread -- "calls" --> main_process_chunk
main_process_chunk_spacy -- "calls" --> main_get_nlp
main_process_chunk_hf -- "calls" --> main_get_hf_ner_pipeline
main_py -- "uses" --> main_save_output

%% Data Flow Relationships
main_py -- "processes" --> ThirdPartyNoticeText_txt
main_py -- "outputs to" --> output_json
main_py -- "reads" --> sample_txt
gen_parse_requirements -- "analyzes" --> requirements_txt
gen_get_gitignore -- "reads" --> gitignore

%% UI Component Relationships
utils_selectNode -- "calls" --> utils_neighbourhoodHighlight
utils_selectNodes -- "calls" --> utils_filterHighlight
utils_js -- "works with" --> vis_network_js
utils_js -- "works with" --> tom_select_js

    </pre>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
