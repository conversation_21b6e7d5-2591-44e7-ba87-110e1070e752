<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Knowledge Graph - Smart Cached Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #f0f8ff; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Codebase Knowledge Graph</h1>
    <div class="info">
        <strong>Smart Cache Status:</strong> Updated analysis from 2025-07-02_12-18-06<br>
        <strong>Repository State:</strong> Uncommitted changes detected - Full re-analysis performed<br>
        <strong>Files Analyzed:</strong> 17 files with detailed entity extraction and relationship mapping
    </div>
    <pre class="mermaid">
graph TD;

%% Root Level Files
main_py["main.py<br/>🐍 NLP Processing Core"]
build_language_library_py["build_language_library.py<br/>🔧 Tree-sitter Builder"]
generate_knowledge_graph_py["generate_knowledge_graph.py<br/>📊 Graph Generator"]
requirements_txt{"requirements.txt<br/>📦 Dependencies"}
spec_prompt_md("spec-prompt.md<br/>📋 Project Spec")
ThirdPartyNoticeText_txt("ThirdPartyNoticeText.txt<br/>📄 Input Data")
gitignore("gitignore<br/>⚙️ Git Config")
output_json("output.json<br/>💾 Results")
sample_txt["sample.txt<br/>📝 Sample Data"]
knowledge_graph_html["knowledge_graph.html<br/>🌐 This Visualization"]

%% Library Files
subgraph "lib - External Libraries"
    subgraph "lib/bindings"
        utils_js["utils.js<br/>🎛️ Graph Interactions"]
    end
    subgraph "lib/tom-select"
        tom_select_js["tom-select.complete.min.js<br/>🎯 Selection UI"]
        tom_select_css["tom-select.css<br/>🎨 Selection Styles"]
    end
    subgraph "lib/vis-9.1.2"
        vis_network_js["vis-network.min.js<br/>🕸️ Network Visualization"]
        vis_network_css["vis-network.css<br/>🎨 Network Styles"]
    end
end

%% Config Files
subgraph ".plandex-v2"
    projects_v2_json("projects-v2.json<br/>🤖 AI Project Config")
end

subgraph "codeanalysis - Smart Cache"
    inventory_old("2025-07-02_12-08-15_code_inventory.json<br/>📊 Previous Cache")
    inventory_new("2025-07-02_12-18-06_code_inventory.json<br/>📊 Current Cache")
end

%% Main.py Functions - NLP Processing Pipeline
subgraph "main.py - NLP Processing Functions"
    main_parse_args(("parse_args<br/>⚙️ CLI Parser"))
    main_chunk_file(("chunk_file<br/>✂️ Text Chunker"))
    main_get_nlp(("get_nlp<br/>🧠 spaCy Loader"))
    main_get_hf_ner_pipeline(("get_hf_ner_pipeline<br/>🤗 HF Pipeline"))
    main_process_chunk_spacy(("process_chunk_spacy<br/>🔍 spaCy NER"))
    main_process_chunk_hf(("process_chunk_hf<br/>🔍 HF NER"))
    main_process_chunk(("process_chunk<br/>🎯 Backend Router"))
    main_worker_thread(("worker_thread<br/>⚡ Thread Worker"))
    main_test_spacy_ner(("test_spacy_ner<br/>🧪 NER Tester"))
    main_save_output(("save_output<br/>💾 File Saver"))
    main_thread_local[["thread_local<br/>🧵 Thread Storage"]]
end

%% Build Language Library Functions - Tree-sitter Setup
subgraph "build_language_library.py - Tree-sitter Builder"
    build_GRAMMARS[["GRAMMARS<br/>📚 Grammar Paths"]]
    build_LIB_PATH[["LIB_PATH<br/>🏗️ Build Output"]]
end

%% Generate Knowledge Graph Functions - Graph Analysis
subgraph "generate_knowledge_graph.py - Graph Generator Functions"
    gen_setup_treesitter_grammars(("setup_treesitter_grammars<br/>🔧 Grammar Setup"))
    gen_get_gitignore(("get_gitignore<br/>📋 Ignore Rules"))
    gen_categorize_file(("categorize_file<br/>🏷️ File Classifier"))
    gen_add_file_node(("add_file_node<br/>➕ Node Creator"))
    gen_parse_requirements(("parse_requirements<br/>📦 Dependency Parser"))
    gen_parse_python_code(("parse_python_code<br/>🐍 Python Analyzer"))
    gen_main(("main<br/>🚀 Entry Point"))
end

%% Utils.js Functions - Interactive Graph Controls
subgraph "utils.js - Graph Interaction Functions"
    utils_neighbourhoodHighlight(("neighbourhoodHighlight<br/>🔍 Node Highlighting"))
    utils_filterHighlight(("filterHighlight<br/>🎯 Filter Display"))
    utils_selectNode(("selectNode<br/>👆 Single Selection"))
    utils_selectNodes(("selectNodes<br/>👆 Multi Selection"))
    utils_highlightFilter(("highlightFilter<br/>🔎 Property Filter"))
end

%% Dependencies - External Libraries
subgraph "External Dependencies"
    dep_spacy{{"spacy[apple]<br/>🧠 NLP Core"}}
    dep_torch{{"torch<br/>🔥 ML Framework"}}
    dep_transformers{{"transformers<br/>🤗 HF Models"}}
    dep_tree_sitter{{"tree-sitter<br/>🌳 Code Parser"}}
    dep_tree_sitter_python{{"tree-sitter-python<br/>🐍 Python Grammar"}}
    dep_tree_sitter_languages{{"tree-sitter-languages<br/>🌐 Multi-language"}}
    dep_argparse{{"argparse<br/>⚙️ CLI Parser"}}
    dep_threading{{"threading<br/>⚡ Concurrency"}}
    dep_json{{"json<br/>📄 Data Format"}}
    dep_os{{"os<br/>💻 System Interface"}}
    dep_datetime{{"datetime<br/>⏰ Time Utils"}}
    dep_pathlib{{"pathlib<br/>📁 Path Utils"}}
    dep_logging{{"logging<br/>📝 Debug Output"}}
    dep_re{{"re<br/>🔍 Regex"}}
    dep_pathspec{{"pathspec<br/>📋 Path Matching"}}
end

%% Dependencies
dep_spacy{"spacy[apple]"}
dep_torch{"torch"}
dep_transformers{"transformers"}
dep_tree_sitter{"tree-sitter"}
dep_tree_sitter_python{"tree-sitter-python"}
dep_tree_sitter_languages{"tree-sitter-languages"}
dep_argparse{"argparse"}
dep_threading{"threading"}
dep_queue{"queue"}
dep_json{"json"}
dep_os{"os"}
dep_datetime{"datetime"}
dep_pathlib{"pathlib"}
dep_logging{"logging"}
dep_re{"re"}
dep_pathspec{"pathspec"}
dep_collections{"collections"}

%% File to Entity Relationships
main_py --> main_parse_args
main_py --> main_chunk_file
main_py --> main_get_nlp
main_py --> main_get_hf_ner_pipeline
main_py --> main_process_chunk_spacy
main_py --> main_process_chunk_hf
main_py --> main_process_chunk
main_py --> main_worker_thread
main_py --> main_test_spacy_ner
main_py --> main_save_output
main_py --> main_thread_local

build_language_library_py --> build_GRAMMARS
build_language_library_py --> build_LIB_PATH

generate_knowledge_graph_py --> gen_setup_treesitter_grammars
generate_knowledge_graph_py --> gen_get_gitignore
generate_knowledge_graph_py --> gen_categorize_file
generate_knowledge_graph_py --> gen_add_file_node
generate_knowledge_graph_py --> gen_parse_requirements
generate_knowledge_graph_py --> gen_parse_python_code
generate_knowledge_graph_py --> gen_main

utils_js --> utils_neighbourhoodHighlight
utils_js --> utils_filterHighlight
utils_js --> utils_selectNode
utils_js --> utils_selectNodes
utils_js --> utils_highlightFilter

%% Dependency Relationships
requirements_txt -- "defines" --> dep_spacy
requirements_txt -- "defines" --> dep_torch
requirements_txt -- "defines" --> dep_transformers
requirements_txt -- "defines" --> dep_tree_sitter
requirements_txt -- "defines" --> dep_tree_sitter_python
requirements_txt -- "defines" --> dep_tree_sitter_languages

main_py -- "imports" --> dep_argparse
main_py -- "imports" --> dep_threading
main_py -- "imports" --> dep_queue
main_py -- "imports" --> dep_spacy
main_py -- "imports" --> dep_json
main_py -- "imports" --> dep_os
main_py -- "imports" --> dep_datetime
main_py -- "imports" --> dep_torch
main_py -- "imports" --> dep_transformers
main_py -- "imports" --> dep_collections
main_py -- "imports" --> dep_re

generate_knowledge_graph_py -- "imports" --> dep_os
generate_knowledge_graph_py -- "imports" --> dep_json
generate_knowledge_graph_py -- "imports" --> dep_re
generate_knowledge_graph_py -- "imports" --> dep_logging
generate_knowledge_graph_py -- "imports" --> dep_pathlib
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter_python
generate_knowledge_graph_py -- "imports" --> dep_tree_sitter_languages
generate_knowledge_graph_py -- "imports" --> dep_pathspec

build_language_library_py -- "imports" --> dep_os
build_language_library_py -- "imports" --> dep_tree_sitter

%% Function Call Relationships
main_process_chunk -- "calls" --> main_process_chunk_spacy
main_process_chunk -- "calls" --> main_process_chunk_hf
main_worker_thread -- "calls" --> main_process_chunk
main_process_chunk_spacy -- "calls" --> main_get_nlp
main_process_chunk_hf -- "calls" --> main_get_hf_ner_pipeline
main_py -- "uses" --> main_save_output

%% Data Flow Relationships
main_py -- "processes" --> ThirdPartyNoticeText_txt
main_py -- "outputs to" --> output_json
main_py -- "reads" --> sample_txt
gen_parse_requirements -- "analyzes" --> requirements_txt
gen_get_gitignore -- "reads" --> gitignore

%% UI Component Relationships
utils_selectNode -- "calls" --> utils_neighbourhoodHighlight
utils_selectNodes -- "calls" --> utils_filterHighlight
utils_js -- "works with" --> vis_network_js
utils_js -- "works with" --> tom_select_js

    </pre>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
