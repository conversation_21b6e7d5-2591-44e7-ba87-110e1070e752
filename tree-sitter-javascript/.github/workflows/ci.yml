name: CI

on:
  push:
    branches: [master]
    paths:
      - grammar.js
      - src/**
      - test/**
      - bindings/**
      - binding.gyp
  pull_request:
    paths:
      - grammar.js
      - src/**
      - test/**
      - bindings/**
      - binding.gyp

concurrency:
  group: ${{github.workflow}}-${{github.ref}}
  cancel-in-progress: true

jobs:
  test:
    name: Test parser
    runs-on: ${{matrix.os}}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-14]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up tree-sitter
        uses: tree-sitter/setup-action/cli@v2
      - name: Set up examples
        run: |-
          git config --global core.longpaths true
          git clone https://github.com/npm/cli examples/cli --single-branch --depth=1 --filter=blob:none
      - name: Run tests
        uses: tree-sitter/parser-test-action@v2
        with:
          test-rust: true
          test-node: true
          test-python: true
          test-go: true
          test-swift: true
      - name: Parse examples
        uses: tree-sitter/parse-action@v4
        with:
          files: |
            examples/*.js
            examples/*.jsx
