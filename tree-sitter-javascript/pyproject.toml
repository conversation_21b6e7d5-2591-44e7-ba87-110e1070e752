[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tree-sitter-javascript"
description = "JavaScript grammar for tree-sitter"
version = "0.23.1"
keywords = ["incremental", "parsing", "tree-sitter", "javascript"]
classifiers = [
  "Intended Audience :: Developers",
  "License :: OSI Approved :: MIT License",
  "Topic :: Software Development :: Compilers",
  "Topic :: Text Processing :: Linguistic",
  "Typing :: Typed",
]
authors = [
  { name = "<PERSON>", email = "<EMAIL>" },
  { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
requires-python = ">=3.9"
license.text = "MIT"
readme = "README.md"

[project.urls]
Homepage = "https://github.com/tree-sitter/tree-sitter-javascript"

[project.optional-dependencies]
core = ["tree-sitter~=0.22"]

[tool.cibuildwheel]
build = "cp39-*"
build-frontend = "build"
