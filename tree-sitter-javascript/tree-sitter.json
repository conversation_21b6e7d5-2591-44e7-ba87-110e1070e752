{"grammars": [{"name": "javascript", "camelcase": "JavaScript", "scope": "source.js", "path": ".", "file-types": ["js", "mjs", "cjs", "jsx"], "highlights": ["queries/highlights.scm", "queries/highlights-jsx.scm", "queries/highlights-params.scm"], "tags": ["queries/tags.scm"], "injection-regex": "^(js|javascript)$"}], "metadata": {"version": "0.23.1", "license": "MIT", "description": "JavaScript grammar for tree-sitter", "authors": [{"name": "<PERSON>", "email": "maxbrun<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "links": {"repository": "https://github.com/tree-sitter/tree-sitter-javascript"}}, "bindings": {"c": true, "go": true, "node": true, "python": true, "rust": true, "swift": true}}