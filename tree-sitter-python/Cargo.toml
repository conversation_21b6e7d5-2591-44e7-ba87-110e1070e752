[package]
name = "tree-sitter-python"
description = "Python grammar for tree-sitter"
version = "0.23.6"
authors = [
  "<PERSON> <maxbrun<PERSON><EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
license = "MIT"
readme = "README.md"
keywords = ["incremental", "parsing", "tree-sitter", "python"]
categories = ["parser-implementations", "parsing", "text-editors"]
repository = "https://github.com/tree-sitter/tree-sitter-python"
edition = "2021"
autoexamples = false

build = "bindings/rust/build.rs"
include = ["LICENSE", "bindings/rust/*", "grammar.js", "queries/*", "src/*", "tree-sitter.json"]

[lib]
path = "bindings/rust/lib.rs"

[dependencies]
tree-sitter-language = "0.1"

[build-dependencies]
cc = "1.1"

[dev-dependencies]
tree-sitter = "0.24"
