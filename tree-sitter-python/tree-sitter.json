{"grammars": [{"name": "python", "camelcase": "Python", "scope": "source.python", "path": ".", "file-types": ["py"], "highlights": "queries/highlights.scm", "tags": "queries/tags.scm", "injection-regex": "py"}], "metadata": {"version": "0.23.6", "license": "MIT", "description": "Python grammar for tree-sitter", "authors": [{"name": "<PERSON>", "email": "maxbrun<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "links": {"repository": "https://github.com/tree-sitter/tree-sitter-python"}}, "bindings": {"c": true, "go": true, "node": true, "python": true, "rust": true, "swift": true}}